import React from "react";
import { Metadata } from "next";
import { getSystemConfig } from "@/app/actions";
import { SiteConfig } from "@/app/types";
import SalesHero from "@/components/sales/hero";
import SalesFeatures from "@/components/sales/features";
import SalesArchitecture from "@/components/sales/architecture";
import SalesPricing from "@/components/sales/pricing";
import SalesCases from "@/components/sales/cases";
import SalesContact from "@/components/sales/contact";

// 页面数据接口
interface SalesPageData {
  title: string;
  description: string;
  keywords: string[];
}

// 生成动态 SEO metadata
export async function generateMetadata(): Promise<Metadata> {
  const siteConfig = await getSystemConfig() as SiteConfig;
  
  const pageData: SalesPageData = {
    title: `商业API销售管理系统 - ${siteConfig.site_name}`,
    description: `${siteConfig.site_name}提供专业的商业API销售管理系统，支持API发布销售、在线支付、工单管理、发票管理、推广返利、VIP会员体系等完整商业化功能，助力企业快速构建API商业生态。`,
    keywords: [
      'API销售管理系统', 
      'API商业化平台', 
      'API接口销售', 
      '在线支付系统', 
      'VIP会员体系', 
      'API生命周期管理',
      '工单管理系统',
      '发票管理',
      '推广返利系统',
      'API文档生成',
      'SDK代码生成'
    ]
  };

  return {
    title: pageData.title,
    description: pageData.description,
    keywords: pageData.keywords.join(', '),
    openGraph: {
      title: pageData.title,
      description: pageData.description,
      type: 'website',
      siteName: siteConfig.site_name,
      images: [
        {
          url: '/images/sales-og.jpg',
          width: 1200,
          height: 630,
          alt: pageData.title,
        }
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title: pageData.title,
      description: pageData.description,
      images: ['/images/sales-og.jpg'],
    },
    alternates: {
      canonical: '/sales',
    },
    category: "API销售管理系统",
    creator: siteConfig.site_name,
    applicationName: siteConfig.site_name,
    authors: [{ name: siteConfig.site_name }],
    generator: siteConfig.site_name,
  };
}

export default async function SalesPage() {
  const siteConfig = await getSystemConfig() as SiteConfig;

  return (
    <div className="w-full max-w-6xl mx-auto">
      {/* Hero 区域 */}
      <SalesHero siteConfig={siteConfig} />
      
      {/* 产品特性 */}
      <SalesFeatures />
      
      {/* 技术架构 */}
      <SalesArchitecture />
      
      {/* 定价方案 */}
      <SalesPricing siteConfig={siteConfig} />
      
      {/* 客户案例 */}
      <SalesCases />
      
      {/* 联系购买 */}
      <SalesContact siteConfig={siteConfig} />
    </div>
  );
}
