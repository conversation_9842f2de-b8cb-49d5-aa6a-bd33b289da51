/* 销售页面样式 */

/* 架构标签页样式 */
.architecture-tabs .ant-tabs-tab {
  font-size: 16px;
  font-weight: 500;
}

.architecture-tabs .ant-tabs-content-holder {
  padding-top: 24px;
}

/* 悬停效果 */
.hover-scale {
  transition: transform 0.3s ease;
}

.hover-scale:hover {
  transform: scale(1.02);
}

/* 渐变背景 */
.gradient-bg {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* 卡片阴影效果 */
.card-shadow {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transition: box-shadow 0.3s ease;
}

.card-shadow:hover {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* 响应式文字大小 */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2rem !important;
    line-height: 1.2 !important;
  }
  
  .hero-subtitle {
    font-size: 1.125rem !important;
  }
}

/* 按钮样式增强 */
.cta-button {
  transition: all 0.3s ease;
  box-shadow: 0 4px 14px 0 rgba(22, 93, 255, 0.39);
}

.cta-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px 0 rgba(22, 93, 255, 0.5);
}

/* 特性卡片样式 */
.feature-card {
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
}

.feature-card:hover {
  border-color: #3b82f6;
  transform: translateY(-4px);
  box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1);
}

/* 指标卡片样式 */
.metric-card {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border: 1px solid #bae6fd;
}

/* 客户案例卡片样式 */
.case-card {
  transition: all 0.3s ease;
}

.case-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 28px 0 rgba(0, 0, 0, 0.12);
}

/* 联系表单样式 */
.contact-form .ant-form-item-label > label {
  font-weight: 500;
  color: #374151;
}

.contact-form .ant-input,
.contact-form .ant-select-selector {
  border-radius: 8px;
}

.contact-form .ant-input:focus,
.contact-form .ant-select-focused .ant-select-selector {
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

/* 标签样式 */
.industry-tag {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  border: 1px solid #93c5fd;
  color: #1e40af;
  font-weight: 500;
}

/* 加载动画 */
.loading-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: .5;
  }
}

/* 滚动条样式 */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* 图片懒加载占位符 */
.image-placeholder {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* 响应式网格 */
.responsive-grid {
  display: grid;
  gap: 1.5rem;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

@media (max-width: 640px) {
  .responsive-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

/* 文字渐变效果 */
.gradient-text {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 成功状态样式 */
.success-indicator {
  color: #10b981;
  background-color: #d1fae5;
  border-color: #a7f3d0;
}

/* 警告状态样式 */
.warning-indicator {
  color: #f59e0b;
  background-color: #fef3c7;
  border-color: #fde68a;
}

/* 错误状态样式 */
.error-indicator {
  color: #ef4444;
  background-color: #fee2e2;
  border-color: #fecaca;
}

/* 定价卡片特殊样式 */
.pricing-card {
  position: relative;
  overflow: hidden;
}

.pricing-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #165DFF 0%, #40a9ff 100%);
}

.pricing-card.popular::before {
  background: linear-gradient(90deg, #52c41a 0%, #73d13d 100%);
}

/* 技术栈进度条样式 */
.tech-progress .ant-progress-bg {
  transition: all 0.3s ease;
}

/* 案例卡片特殊效果 */
.case-study-card {
  position: relative;
  overflow: hidden;
}

.case-study-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.case-study-card:hover::after {
  left: 100%;
}
