"use client";

import React from "react";
import {
  Card,
  Row,
  Col,
  Typo<PERSON>,
  Button,
  Space
} from "antd";
import {
  PhoneOutlined,
  MailOutlined,
  WechatOutlined,
  QqOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  RocketOutlined,
  CustomerServiceOutlined,
  ShoppingCartOutlined
} from "@ant-design/icons";
import { SiteConfig } from "@/app/types";
import Link from "next/link";

const { Title, Paragraph } = Typography;

interface SalesContactProps {
  siteConfig: SiteConfig;
}

const SalesContact: React.FC<SalesContactProps> = ({ siteConfig }) => {

  const contactMethods = [
    {
      icon: <PhoneOutlined className="text-2xl text-blue-600" />,
      title: "电话咨询",
      content: "************",
      description: "工作日 9:00-18:00"
    },
    {
      icon: <MailOutlined className="text-2xl text-blue-600" />,
      title: "邮件联系",
      content: "<EMAIL>",
      description: "24小时内回复"
    },
    {
      icon: <WechatOutlined className="text-2xl text-blue-600" />,
      title: "微信咨询",
      content: "扫码添加销售微信",
      description: "专属销售顾问"
    },
    {
      icon: <QqOutlined className="text-2xl text-blue-600" />,
      title: "QQ咨询",
      content: "123456789",
      description: "在线客服"
    }
  ];

  const serviceAdvantages = [
    "免费产品演示",
    "专业方案设计",
    "协助部署上线",
    "1年免费技术支持",
    "定制开发服务",
    "7x24小时售后"
  ];

  return (
    <div id="contact-section" className="w-full py-20 px-4 lg:px-8 xl:px-16">
      {/* 标题区域 */}
      <div className="text-center mb-16">
        <Title level={2} className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
          联系购买
        </Title>
        <Paragraph className="text-xl text-gray-600 max-w-3xl mx-auto">
          专业的销售团队和技术支持，为您提供一对一的咨询服务
        </Paragraph>
      </div>

      {/* 购买按钮区域 */}
      <div className="text-center mb-16">
        <div className="bg-gradient-to-r from-blue-50 to-blue-100 rounded-2xl p-8 max-w-4xl mx-auto">
          <Title level={3} className="text-gray-900 mb-4">
            立即购买商业API销售管理系统
          </Title>
          <Paragraph className="text-gray-700 text-lg mb-6">
            一次性购买，终身使用。包含完整源代码和1年免费技术支持
          </Paragraph>
          <Space size="large" className="flex flex-col sm:flex-row justify-center">
            <Link href="/vip">
              <Button
                type="primary"
                size="large"
                className="bg-blue-600 hover:bg-blue-700 border-blue-600 hover:border-blue-700 px-8 py-3 h-auto text-base font-semibold"
                icon={<ShoppingCartOutlined />}
              >
                立即购买
              </Button>
            </Link>
            <Button
              size="large"
              className="border-blue-600 text-blue-600 hover:border-blue-700 hover:text-blue-700 px-8 py-3 h-auto text-base font-semibold"
              icon={<CustomerServiceOutlined />}
            >
              咨询客服
            </Button>
          </Space>
        </div>
      </div>

      <Row gutter={[32, 32]}>
        {/* 联系方式 */}
        <Col xs={24} lg={12}>
          <Card className="shadow-lg h-full">
            <Title level={4} className="text-gray-900 mb-6 flex items-center">
              <CustomerServiceOutlined className="mr-2 text-blue-600" />
              联系方式
            </Title>
            <div className="space-y-4">
              {contactMethods.map((method, index) => (
                <div key={index} className="flex items-center space-x-4 p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                  {method.icon}
                  <div>
                    <div className="font-semibold text-gray-900">{method.title}</div>
                    <div className="text-blue-600 font-medium">{method.content}</div>
                    <div className="text-sm text-gray-600">{method.description}</div>
                  </div>
                </div>
              ))}
            </div>
          </Card>
        </Col>

        {/* 服务优势 */}
        <Col xs={24} lg={12}>
          <Card className="shadow-lg h-full">
            <Title level={4} className="text-gray-900 mb-6">
              服务优势
            </Title>
            <div className="space-y-4">
              {serviceAdvantages.map((advantage, index) => (
                <div key={index} className="flex items-center space-x-3 p-3 bg-green-50 rounded-lg">
                  <CheckCircleOutlined className="text-green-500 flex-shrink-0 text-lg" />
                  <span className="text-gray-700 font-medium">{advantage}</span>
                </div>
              ))}
            </div>

            {/* 响应时间 */}
            <div className="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
              <div className="flex items-center justify-center space-x-3">
                <ClockCircleOutlined className="text-2xl text-blue-600" />
                <div>
                  <div className="font-semibold text-blue-900">快速响应</div>
                  <div className="text-sm text-blue-700">
                    咨询后 <span className="font-bold">2小时内</span> 回复
                  </div>
                </div>
              </div>
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default SalesContact;
