"use client";

import React, { useState } from "react";
import { Card, Row, Col, Typography, Tabs, Tag } from "antd";
import {
  CloudOutlined,
  DatabaseOutlined,
  ApiOutlined,
  SafetyCertificateOutlined,
  MonitorOutlined,
  SettingOutlined
} from "@ant-design/icons";
import Image from "next/image";

const { Title, Paragraph } = Typography;
const { TabPane } = Tabs;

interface TechStackItem {
  category: string;
  icon: React.ReactNode;
  technologies: {
    name: string;
    description: string;
    version?: string;
  }[];
}

const techStack: TechStackItem[] = [
  {
    category: "前端技术",
    icon: <CloudOutlined className="text-2xl text-blue-600" />,
    technologies: [
      { name: "React 18", description: "现代化前端框架", version: "18.x" },
      { name: "Next.js", description: "全栈React框架", version: "14.x" },
      { name: "TypeScript", description: "类型安全的JavaScript", version: "5.x" },
      { name: "Ant Design", description: "企业级UI组件库", version: "5.x" },
      { name: "TailwindCSS", description: "原子化CSS框架", version: "3.x" }
    ]
  },
  {
    category: "后端技术",
    icon: <DatabaseOutlined className="text-2xl text-blue-600" />,
    technologies: [
      { name: "Node.js", description: "高性能JavaScript运行时", version: "20.x" },
      { name: "Express.js", description: "轻量级Web框架", version: "4.x" },
      { name: "MySQL", description: "关系型数据库", version: "8.0" },
      { name: "Redis", description: "内存数据库", version: "7.x" },
      { name: "Elasticsearch", description: "搜索引擎", version: "8.x" }
    ]
  },
  {
    category: "API网关",
    icon: <ApiOutlined className="text-2xl text-blue-600" />,
    technologies: [
      { name: "Kong", description: "云原生API网关", version: "3.x" },
      { name: "Nginx", description: "高性能Web服务器", version: "1.24" },
      { name: "OpenAPI", description: "API规范标准", version: "3.0" },
      { name: "GraphQL", description: "查询语言和运行时", version: "16.x" },
      { name: "gRPC", description: "高性能RPC框架", version: "1.x" }
    ]
  },
  {
    category: "安全防护",
    icon: <SafetyCertificateOutlined className="text-2xl text-blue-600" />,
    technologies: [
      { name: "OAuth 2.0", description: "授权框架标准" },
      { name: "JWT", description: "JSON Web Token" },
      { name: "SSL/TLS", description: "传输层安全协议" },
      { name: "WAF", description: "Web应用防火墙" },
      { name: "Rate Limiting", description: "访问频率限制" }
    ]
  },
  {
    category: "监控运维",
    icon: <MonitorOutlined className="text-2xl text-blue-600" />,
    technologies: [
      { name: "Prometheus", description: "监控系统", version: "2.x" },
      { name: "Grafana", description: "可视化平台", version: "10.x" },
      { name: "ELK Stack", description: "日志分析平台" },
      { name: "Docker", description: "容器化平台", version: "24.x" },
      { name: "Kubernetes", description: "容器编排平台", version: "1.28" }
    ]
  },
  {
    category: "部署运维",
    icon: <SettingOutlined className="text-2xl text-blue-600" />,
    technologies: [
      { name: "CI/CD", description: "持续集成/部署" },
      { name: "GitLab", description: "代码管理平台" },
      { name: "Ansible", description: "自动化运维工具" },
      { name: "Terraform", description: "基础设施即代码" },
      { name: "Helm", description: "Kubernetes包管理器" }
    ]
  }
];

const architectureLayers = [
  {
    name: "用户层",
    description: "Web管理后台、移动端应用、开发者门户",
    color: "bg-blue-100 border-blue-300"
  },
  {
    name: "接入层",
    description: "负载均衡、API网关、CDN加速",
    color: "bg-green-100 border-green-300"
  },
  {
    name: "应用层",
    description: "API管理服务、用户管理、权限控制",
    color: "bg-yellow-100 border-yellow-300"
  },
  {
    name: "数据层",
    description: "MySQL、Redis、Elasticsearch",
    color: "bg-purple-100 border-purple-300"
  },
  {
    name: "基础设施层",
    description: "容器化部署、监控告警、日志收集",
    color: "bg-gray-100 border-gray-300"
  }
];

const PrivateDeploymentArchitecture: React.FC = () => {
  const [activeTab, setActiveTab] = useState("overview");

  return (
    <div className="w-full max-w-6xl mx-auto py-20 px-4 lg:px-0">
      {/* 标题区域 */}
      <div className="text-center mb-16">
        <Title level={2} className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
          技术架构
        </Title>
        <Paragraph className="text-xl text-gray-600 max-w-3xl mx-auto">
          基于现代化技术栈构建的高可用、高性能、高安全的企业级API管理平台
        </Paragraph>
      </div>

      <Tabs 
        activeKey={activeTab} 
        onChange={setActiveTab}
        size="large"
        className="architecture-tabs"
      >
        {/* 架构概览 */}
        <TabPane tab="架构概览" key="overview">
          <Row gutter={[24, 24]}>
            <Col xs={24} lg={12}>
              <Card className="h-full">
                <Title level={4} className="text-gray-900 mb-4">系统架构图</Title>
                <div className="relative">
                  <div className="w-full h-64 bg-gradient-to-br from-gray-100 to-gray-200 rounded-lg flex items-center justify-center border-2 border-dashed border-gray-300">
                    <div className="text-center">
                      <DatabaseOutlined className="text-4xl text-gray-500 mb-2" />
                      <div className="text-sm text-gray-600">系统架构图</div>
                    </div>
                  </div>
                </div>
              </Card>
            </Col>
            
            <Col xs={24} lg={12}>
              <Card className="h-full">
                <Title level={4} className="text-gray-900 mb-4">架构层次</Title>
                <div className="space-y-4">
                  {architectureLayers.map((layer, index) => (
                    <div 
                      key={index}
                      className={`p-4 rounded-lg border-2 ${layer.color}`}
                    >
                      <div className="font-semibold text-gray-900 mb-2">
                        {layer.name}
                      </div>
                      <div className="text-sm text-gray-700">
                        {layer.description}
                      </div>
                    </div>
                  ))}
                </div>
              </Card>
            </Col>
          </Row>
        </TabPane>

        {/* 技术栈 */}
        <TabPane tab="技术栈" key="techstack">
          <Row gutter={[24, 24]}>
            {techStack.map((stack, index) => (
              <Col xs={24} sm={12} lg={8} key={index}>
                <Card className="h-full">
                  <div className="flex items-center mb-4">
                    {stack.icon}
                    <Title level={5} className="ml-3 mb-0 text-gray-900">
                      {stack.category}
                    </Title>
                  </div>
                  
                  <div className="space-y-3">
                    {stack.technologies.map((tech, techIndex) => (
                      <div key={techIndex} className="border-l-2 border-blue-200 pl-3">
                        <div className="flex items-center justify-between mb-1">
                          <span className="font-medium text-gray-900">{tech.name}</span>
                          {tech.version && (
                            <Tag size="small" color="blue">{tech.version}</Tag>
                          )}
                        </div>
                        <div className="text-sm text-gray-600">{tech.description}</div>
                      </div>
                    ))}
                  </div>
                </Card>
              </Col>
            ))}
          </Row>
        </TabPane>

        {/* 部署架构 */}
        <TabPane tab="部署架构" key="deployment">
          <Row gutter={[24, 24]}>
            <Col xs={24} lg={16}>
              <Card>
                <Title level={4} className="text-gray-900 mb-4">部署架构图</Title>
                <div className="relative">
                  <div className="w-full h-64 bg-gradient-to-br from-blue-100 to-blue-200 rounded-lg flex items-center justify-center border-2 border-dashed border-blue-300">
                    <div className="text-center">
                      <CloudOutlined className="text-4xl text-blue-600 mb-2" />
                      <div className="text-sm text-blue-700">部署架构图</div>
                    </div>
                  </div>
                </div>
              </Card>
            </Col>
            
            <Col xs={24} lg={8}>
              <Card className="h-full">
                <Title level={4} className="text-gray-900 mb-4">部署特性</Title>
                <div className="space-y-4">
                  <div className="p-3 bg-blue-50 rounded-lg">
                    <div className="font-semibold text-blue-900 mb-1">高可用性</div>
                    <div className="text-sm text-blue-700">多节点集群部署，故障自动切换</div>
                  </div>
                  
                  <div className="p-3 bg-green-50 rounded-lg">
                    <div className="font-semibold text-green-900 mb-1">弹性扩容</div>
                    <div className="text-sm text-green-700">根据负载自动扩缩容</div>
                  </div>
                  
                  <div className="p-3 bg-yellow-50 rounded-lg">
                    <div className="font-semibold text-yellow-900 mb-1">容器化</div>
                    <div className="text-sm text-yellow-700">Docker + Kubernetes部署</div>
                  </div>
                  
                  <div className="p-3 bg-purple-50 rounded-lg">
                    <div className="font-semibold text-purple-900 mb-1">监控告警</div>
                    <div className="text-sm text-purple-700">全方位系统监控和告警</div>
                  </div>
                </div>
              </Card>
            </Col>
          </Row>
        </TabPane>

        {/* 安全架构 */}
        <TabPane tab="安全架构" key="security">
          <Row gutter={[24, 24]}>
            <Col xs={24}>
              <Card>
                <Title level={4} className="text-gray-900 mb-6">安全防护体系</Title>
                
                <Row gutter={[24, 24]}>
                  <Col xs={24} sm={12} lg={6}>
                    <div className="text-center p-6 bg-red-50 rounded-lg">
                      <SafetyCertificateOutlined className="text-3xl text-red-600 mb-3" />
                      <div className="font-semibold text-red-900 mb-2">网络安全</div>
                      <div className="text-sm text-red-700">
                        防火墙、VPN、网络隔离
                      </div>
                    </div>
                  </Col>
                  
                  <Col xs={24} sm={12} lg={6}>
                    <div className="text-center p-6 bg-blue-50 rounded-lg">
                      <SafetyCertificateOutlined className="text-3xl text-blue-600 mb-3" />
                      <div className="font-semibold text-blue-900 mb-2">应用安全</div>
                      <div className="text-sm text-blue-700">
                        身份认证、权限控制、API安全
                      </div>
                    </div>
                  </Col>
                  
                  <Col xs={24} sm={12} lg={6}>
                    <div className="text-center p-6 bg-green-50 rounded-lg">
                      <SafetyCertificateOutlined className="text-3xl text-green-600 mb-3" />
                      <div className="font-semibold text-green-900 mb-2">数据安全</div>
                      <div className="text-sm text-green-700">
                        数据加密、备份恢复、隐私保护
                      </div>
                    </div>
                  </Col>
                  
                  <Col xs={24} sm={12} lg={6}>
                    <div className="text-center p-6 bg-yellow-50 rounded-lg">
                      <SafetyCertificateOutlined className="text-3xl text-yellow-600 mb-3" />
                      <div className="font-semibold text-yellow-900 mb-2">运维安全</div>
                      <div className="text-sm text-yellow-700">
                        安全审计、日志监控、合规管理
                      </div>
                    </div>
                  </Col>
                </Row>
              </Card>
            </Col>
          </Row>
        </TabPane>
      </Tabs>
    </div>
  );
};

export default PrivateDeploymentArchitecture;
