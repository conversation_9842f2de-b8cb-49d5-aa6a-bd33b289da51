"use client";

import React, { useState } from "react";
import { Card, Row, Col, Typography, Tabs, Tag, Progress } from "antd";
import type { TabsProps } from "antd";
import {
  CloudOutlined,
  DatabaseOutlined,
  ApiOutlined,
  SafetyCertificateOutlined,
  MonitorOutlined,
  ThunderboltOutlined
} from "@ant-design/icons";

const { Title, Paragraph } = Typography;

interface TechStackItem {
  name: string;
  description: string;
  version?: string;
  usage: number;
}

interface TechCategory {
  category: string;
  icon: React.ReactNode;
  color: string;
  technologies: TechStackItem[];
}

const techStack: TechCategory[] = [
  {
    category: "前端技术",
    icon: <CloudOutlined className="text-2xl" />,
    color: "blue",
    technologies: [
      { name: "Next.js", description: "React全栈框架", version: "14.x", usage: 95 },
      { name: "Ant Design", description: "企业级UI组件库", version: "5.x", usage: 90 },
      { name: "TailwindCSS", description: "原子化CSS框架", version: "3.x", usage: 85 },
      { name: "TypeScript", description: "类型安全的JavaScript", version: "5.x", usage: 100 }
    ]
  },
  {
    category: "后端技术",
    icon: <DatabaseOutlined className="text-2xl" />,
    color: "green",
    technologies: [
      { name: "PHP + Swoole", description: "高性能PHP框架", version: "8.x", usage: 90 },
      { name: "Hyperf", description: "企业级微服务框架", version: "3.x", usage: 85 },
      { name: "Go", description: "高并发处理服务", version: "1.21", usage: 80 },
      { name: "MySQL", description: "关系型数据库", version: "8.0", usage: 95 }
    ]
  },
  {
    category: "数据存储",
    icon: <DatabaseOutlined className="text-2xl" />,
    color: "purple",
    technologies: [
      { name: "MySQL", description: "主数据库", version: "8.0", usage: 100 },
      { name: "ClickHouse", description: "大数据分析", version: "23.x", usage: 75 },
      { name: "Redis", description: "缓存和会话", version: "7.x", usage: 90 },
      { name: "Elasticsearch", description: "搜索引擎", version: "8.x", usage: 70 }
    ]
  },
  {
    category: "消息队列",
    icon: <ApiOutlined className="text-2xl" />,
    color: "orange",
    technologies: [
      { name: "Redis Queue", description: "轻量级队列", usage: 85 },
      { name: "Kafka", description: "大数据流处理", usage: 80 },
      { name: "RabbitMQ", description: "消息中间件", usage: 75 },
      { name: "NATS", description: "云原生消息系统", usage: 70 }
    ]
  }
];

const architectureFeatures = [
  {
    title: "高性能架构",
    description: "基于Swoole的高性能PHP框架，支持协程和异步IO",
    icon: <ThunderboltOutlined className="text-3xl text-yellow-600" />,
    metrics: [
      { label: "并发处理", value: "10万+/秒" },
      { label: "响应时间", value: "<50ms" },
      { label: "系统可用性", value: "99.9%" }
    ]
  },
  {
    title: "微服务架构",
    description: "基于Hyperf的企业级微服务架构，支持服务发现和负载均衡",
    icon: <CloudOutlined className="text-3xl text-blue-600" />,
    metrics: [
      { label: "服务模块", value: "20+" },
      { label: "部署节点", value: "50+" },
      { label: "负载均衡", value: "自动" }
    ]
  },
  {
    title: "数据安全",
    description: "多层次数据安全保护，支持数据加密和访问控制",
    icon: <SafetyCertificateOutlined className="text-3xl text-green-600" />,
    metrics: [
      { label: "数据加密", value: "AES-256" },
      { label: "访问控制", value: "RBAC" },
      { label: "安全审计", value: "全覆盖" }
    ]
  },
  {
    title: "实时监控",
    description: "全方位系统监控和性能分析，支持实时告警",
    icon: <MonitorOutlined className="text-3xl text-red-600" />,
    metrics: [
      { label: "监控指标", value: "100+" },
      { label: "告警响应", value: "<1分钟" },
      { label: "日志存储", value: "TB级" }
    ]
  }
];

const SalesArchitecture: React.FC = () => {
  const [activeTab, setActiveTab] = useState("overview");

  const tabItems: TabsProps['items'] = [
    {
      key: 'overview',
      label: '架构概览',
      children: (
        <div>
          <Row gutter={[24, 24]}>
            {architectureFeatures.map((feature, index) => (
              <Col xs={24} sm={12} lg={6} key={index}>
                <Card className="h-full text-center hover:shadow-lg transition-shadow duration-300">
                  <div className="mb-4">{feature.icon}</div>
                  <Title level={5} className="text-gray-900 mb-3">
                    {feature.title}
                  </Title>
                  <Paragraph className="text-gray-600 text-sm mb-4">
                    {feature.description}
                  </Paragraph>
                  <div className="space-y-2">
                    {feature.metrics.map((metric, idx) => (
                      <div key={idx} className="flex justify-between text-sm">
                        <span className="text-gray-600">{metric.label}:</span>
                        <span className="font-semibold text-blue-600">{metric.value}</span>
                      </div>
                    ))}
                  </div>
                </Card>
              </Col>
            ))}
          </Row>
        </div>
      )
    },
    {
      key: 'techstack',
      label: '技术栈',
      children: (
        <Row gutter={[24, 24]}>
          {techStack.map((stack, index) => (
            <Col xs={24} sm={12} lg={6} key={index}>
              <Card className="h-full">
                <div className="flex items-center mb-4">
                  <div className={`text-${stack.color}-600`}>{stack.icon}</div>
                  <Title level={5} className="ml-3 mb-0 text-gray-900">
                    {stack.category}
                  </Title>
                </div>
                
                <div className="space-y-4">
                  {stack.technologies.map((tech, techIndex) => (
                    <div key={techIndex} className="border-l-2 border-blue-200 pl-3">
                      <div className="flex items-center justify-between mb-1">
                        <span className="font-medium text-gray-900">{tech.name}</span>
                        {tech.version && (
                          <Tag size="small" color={stack.color}>{tech.version}</Tag>
                        )}
                      </div>
                      <div className="text-sm text-gray-600 mb-2">{tech.description}</div>
                      {tech.usage && (
                        <Progress 
                          percent={tech.usage} 
                          size="small" 
                          strokeColor={`var(--ant-color-${stack.color})`}
                          showInfo={false}
                        />
                      )}
                    </div>
                  ))}
                </div>
              </Card>
            </Col>
          ))}
        </Row>
      )
    },
    {
      key: 'performance',
      label: '性能指标',
      children: (
        <Row gutter={[24, 24]}>
          <Col xs={24} lg={16}>
            <Card>
              <Title level={4} className="text-gray-900 mb-4">系统性能表现</Title>
              <div className="space-y-6">
                <div>
                  <div className="flex justify-between mb-2">
                    <span className="text-gray-700">API响应时间</span>
                    <span className="font-semibold text-green-600">&lt;50ms</span>
                  </div>
                  <Progress percent={95} strokeColor="#52c41a" />
                </div>
                
                <div>
                  <div className="flex justify-between mb-2">
                    <span className="text-gray-700">并发处理能力</span>
                    <span className="font-semibold text-blue-600">10万+/秒</span>
                  </div>
                  <Progress percent={90} strokeColor="#1677ff" />
                </div>
                
                <div>
                  <div className="flex justify-between mb-2">
                    <span className="text-gray-700">系统可用性</span>
                    <span className="font-semibold text-purple-600">99.9%</span>
                  </div>
                  <Progress percent={99.9} strokeColor="#722ed1" />
                </div>
                
                <div>
                  <div className="flex justify-between mb-2">
                    <span className="text-gray-700">数据处理速度</span>
                    <span className="font-semibold text-orange-600">TB级/天</span>
                  </div>
                  <Progress percent={85} strokeColor="#fa8c16" />
                </div>
              </div>
            </Card>
          </Col>
          
          <Col xs={24} lg={8}>
            <Card className="h-full">
              <Title level={4} className="text-gray-900 mb-4">技术优势</Title>
              <div className="space-y-4">
                <div className="p-3 bg-blue-50 rounded-lg">
                  <div className="font-semibold text-blue-900 mb-1">高并发处理</div>
                  <div className="text-sm text-blue-700">基于Swoole协程技术</div>
                </div>
                
                <div className="p-3 bg-green-50 rounded-lg">
                  <div className="font-semibold text-green-900 mb-1">微服务架构</div>
                  <div className="text-sm text-green-700">Hyperf企业级框架</div>
                </div>
                
                <div className="p-3 bg-yellow-50 rounded-lg">
                  <div className="font-semibold text-yellow-900 mb-1">大数据分析</div>
                  <div className="text-sm text-yellow-700">ClickHouse实时分析</div>
                </div>
                
                <div className="p-3 bg-purple-50 rounded-lg">
                  <div className="font-semibold text-purple-900 mb-1">消息队列</div>
                  <div className="text-sm text-purple-700">多种MQ技术支持</div>
                </div>
              </div>
            </Card>
          </Col>
        </Row>
      )
    }
  ];

  return (
    <div className="w-full max-w-6xl mx-auto py-20 px-4 lg:px-0 bg-gray-50">
      {/* 标题区域 */}
      <div className="text-center mb-16">
        <Title level={2} className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
          高性能技术架构
        </Title>
        <Paragraph className="text-xl text-gray-600 max-w-3xl mx-auto">
          基于现代化技术栈构建的高可用、高性能、高安全的企业级API销售管理平台
        </Paragraph>
      </div>

      <Card className="shadow-lg">
        <Tabs 
          activeKey={activeTab} 
          onChange={setActiveTab}
          size="large"
          className="architecture-tabs"
          items={tabItems}
        />
      </Card>
    </div>
  );
};

export default SalesArchitecture;
