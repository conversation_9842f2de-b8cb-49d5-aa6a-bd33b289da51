"use client";

import React, { useState } from "react";
import { Card, Row, Col, Typography, Button, Tag, Tabs } from "antd";
import type { TabsProps } from "antd";
import {
  BuildOutlined,
  BankOutlined,
  ShopOutlined,
  GlobalOutlined,
  CheckCircleOutlined,
  StarOutlined
} from "@ant-design/icons";

const { Title, Paragraph } = Typography;

interface SolutionPlan {
  id: string;
  name: string;
  icon: React.ReactNode;
  description: string;
  targetUsers: string[];
  features: string[];
  deployment: string[];
  support: string[];
  price: string;
  popular?: boolean;
}

const solutions: SolutionPlan[] = [
  {
    id: "startup",
    name: "初创版",
    icon: <BuildOutlined className="text-3xl text-blue-600" />,
    description: "适合初创企业和小型团队的轻量级解决方案",
    targetUsers: ["初创企业", "小型团队", "个人开发者", "技术验证"],
    features: [
      "基础API管理功能",
      "简单权限控制",
      "基础监控面板",
      "标准文档生成",
      "邮件技术支持",
      "社区版功能"
    ],
    deployment: [
      "单机部署",
      "Docker容器",
      "基础配置",
      "标准安装包"
    ],
    support: [
      "邮件技术支持",
      "在线文档",
      "社区论坛",
      "基础培训"
    ],
    price: "联系咨询"
  },
  {
    id: "enterprise",
    name: "企业版",
    icon: <BankOutlined className="text-3xl text-blue-600" />,
    description: "面向中大型企业的完整解决方案",
    targetUsers: ["中型企业", "大型企业", "政府机构", "金融机构"],
    features: [
      "完整API生命周期管理",
      "高级权限控制",
      "实时监控告警",
      "自动化测试",
      "多环境管理",
      "高级分析报告",
      "SSO单点登录",
      "API网关集成"
    ],
    deployment: [
      "集群部署",
      "高可用架构",
      "负载均衡",
      "数据备份",
      "灾备方案"
    ],
    support: [
      "7x24技术支持",
      "专属客户经理",
      "现场技术服务",
      "定制化培训",
      "系统优化服务"
    ],
    price: "联系咨询",
    popular: true
  },
  {
    id: "saas",
    name: "SaaS版",
    icon: <ShopOutlined className="text-3xl text-blue-600" />,
    description: "为SaaS服务商提供的专业API管理平台",
    targetUsers: ["SaaS服务商", "API提供商", "平台型企业", "开放平台"],
    features: [
      "多租户架构",
      "开放平台管理",
      "开发者门户",
      "API商店功能",
      "计费管理系统",
      "第三方集成",
      "白标定制",
      "API市场"
    ],
    deployment: [
      "云原生架构",
      "微服务部署",
      "弹性扩容",
      "全球CDN",
      "多区域部署"
    ],
    support: [
      "专业咨询服务",
      "架构设计支持",
      "业务对接服务",
      "运营支持",
      "市场推广支持"
    ],
    price: "联系咨询"
  },
  {
    id: "global",
    name: "国际版",
    icon: <GlobalOutlined className="text-3xl text-blue-600" />,
    description: "面向跨国企业的全球化部署方案",
    targetUsers: ["跨国企业", "国际组织", "全球化公司", "海外业务"],
    features: [
      "多语言支持",
      "多时区管理",
      "国际合规认证",
      "全球数据同步",
      "跨境数据传输",
      "本地化定制",
      "多币种支持",
      "国际标准接口"
    ],
    deployment: [
      "全球多点部署",
      "跨区域同步",
      "就近访问优化",
      "国际网络优化",
      "合规数据存储"
    ],
    support: [
      "全球技术支持",
      "多语言服务",
      "本地化服务",
      "合规咨询",
      "国际认证支持"
    ],
    price: "联系咨询"
  }
];

const PrivateDeploymentSolutions: React.FC = () => {
  const [activeTab, setActiveTab] = useState("enterprise");

  const tabItems: TabsProps['items'] = solutions.map((solution) => ({
    key: solution.id,
    label: solution.name,
    children: (
        <Row gutter={[32, 32]}>
          <Col xs={24} lg={12}>
            <div className="mb-6">
              <Title level={3} className="text-gray-900 mb-3 flex items-center">
                {solution.icon}
                <span className="ml-3">{solution.name}</span>
              </Title>
              <Paragraph className="text-gray-600 text-lg">
                {solution.description}
              </Paragraph>
            </div>

            <div className="mb-6">
              <Title level={5} className="text-gray-900 mb-3">适用对象</Title>
              <div className="flex flex-wrap gap-2">
                {solution.targetUsers.map((user, index) => (
                  <Tag key={index} color="blue-inverse" className="mb-1">
                    {user}
                  </Tag>
                ))}
              </div>
            </div>
          </Col>

          <Col xs={24} lg={12}>
            <div className="space-y-6">
              <div>
                <Title level={5} className="text-gray-900 mb-3">核心功能</Title>
                <div className="space-y-2">
                  {solution.features.map((feature, index) => (
                    <div key={index} className="flex items-center text-gray-700">
                      <CheckCircleOutlined className="text-green-500 mr-2 flex-shrink-0" />
                      <span>{feature}</span>
                    </div>
                  ))}
                </div>
              </div>

              <div>
                <Title level={5} className="text-gray-900 mb-3">部署方式</Title>
                <div className="space-y-2">
                  {solution.deployment.map((deploy, index) => (
                    <div key={index} className="flex items-center text-gray-700">
                      <CheckCircleOutlined className="text-blue-500 mr-2 flex-shrink-0" />
                      <span>{deploy}</span>
                    </div>
                  ))}
                </div>
              </div>

              <div>
                <Title level={5} className="text-gray-900 mb-3">技术支持</Title>
                <div className="space-y-2">
                  {solution.support.map((support, index) => (
                    <div key={index} className="flex items-center text-gray-700">
                      <CheckCircleOutlined className="text-orange-500 mr-2 flex-shrink-0" />
                      <span>{support}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </Col>
        </Row>
      )
    }));

  return (
    <div className="w-full max-w-6xl mx-auto py-20 px-4 lg:px-0 bg-gray-50">
      {/* 标题区域 */}
      <div className="text-center mb-16">
        <Title level={2} className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
          部署解决方案
        </Title>
        <Paragraph className="text-xl text-gray-600 max-w-3xl mx-auto">
          针对不同规模和需求的企业，提供量身定制的私有化部署方案
        </Paragraph>
      </div>

      {/* 方案卡片网格 */}
      <Row gutter={[24, 24]} className="mb-16">
        {solutions.map((solution) => (
          <Col xs={24} sm={12} lg={6} key={solution.id}>
            <Card
              className={`h-full hover:shadow-xl transition-all duration-300 cursor-pointer relative ${
                solution.popular ? 'border-blue-500 shadow-lg' : 'border-gray-200'
              }`}
              bodyStyle={{ padding: '24px' }}
              onClick={() => setActiveTab(solution.id)}
            >
              {solution.popular && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <Tag color="blue" className="px-3 py-1 text-sm font-medium">
                    <StarOutlined className="mr-1" />
                    推荐方案
                  </Tag>
                </div>
              )}

              <div className="text-center mb-4">
                {solution.icon}
              </div>

              <Title level={4} className="text-center text-gray-900 mb-3">
                {solution.name}
              </Title>

              <Paragraph className="text-gray-600 text-center mb-4 text-sm">
                {solution.description}
              </Paragraph>

              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600 mb-2">
                  {solution.price}
                </div>
                <Button
                  type={solution.popular ? "primary" : "default"}
                  size="small"
                  className={solution.popular ? "bg-blue-600 border-blue-600" : ""}
                >
                  查看详情
                </Button>
              </div>
            </Card>
          </Col>
        ))}
      </Row>

      {/* 详细方案展示 */}
      <Card className="shadow-lg">
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          size="large"
          className="solution-tabs"
          items={tabItems}
        />
      </Card>
    </div>
  );
};

export default PrivateDeploymentSolutions;
