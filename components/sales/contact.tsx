"use client";

import React, { useState } from "react";
import { 
  Card, 
  Row, 
  Col, 
  Typography, 
  Button, 
  Form, 
  Input, 
  Select, 
  message,
  Space
} from "antd";
import {
  PhoneOutlined,
  MailOutlined,
  WechatOutlined,
  QqOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  RocketOutlined,
  CustomerServiceOutlined
} from "@ant-design/icons";
import { SiteConfig } from "@/app/types";

const { Title, Paragraph } = Typography;
const { Option } = Select;
const { TextArea } = Input;

interface SalesContactProps {
  siteConfig: SiteConfig;
}

interface ContactFormData {
  name: string;
  company: string;
  position: string;
  phone: string;
  email: string;
  industry: string;
  scale: string;
  requirements: string;
  budget: string;
}

const SalesContact: React.FC<SalesContactProps> = ({ siteConfig }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (values: ContactFormData) => {
    try {
      setLoading(true);
      
      // 这里应该调用实际的API提交表单
      console.log('提交咨询表单:', values);
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      message.success('提交成功！我们将在2小时内与您联系');
      form.resetFields();
      
    } catch (error) {
      message.error('提交失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  const contactMethods = [
    {
      icon: <PhoneOutlined className="text-2xl text-blue-600" />,
      title: "电话咨询",
      content: "************",
      description: "工作日 9:00-18:00"
    },
    {
      icon: <MailOutlined className="text-2xl text-blue-600" />,
      title: "邮件联系",
      content: "<EMAIL>",
      description: "24小时内回复"
    },
    {
      icon: <WechatOutlined className="text-2xl text-blue-600" />,
      title: "微信咨询",
      content: "扫码添加销售微信",
      description: "专属销售顾问"
    },
    {
      icon: <QqOutlined className="text-2xl text-blue-600" />,
      title: "QQ咨询",
      content: "123456789",
      description: "在线客服"
    }
  ];

  const serviceAdvantages = [
    "免费产品演示",
    "专业方案设计",
    "协助部署上线",
    "1年免费技术支持",
    "定制开发服务",
    "7x24小时售后"
  ];

  return (
    <div id="contact-section" className="w-full max-w-6xl mx-auto py-20 px-4 lg:px-0">
      {/* 标题区域 */}
      <div className="text-center mb-16">
        <Title level={2} className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
          联系我们
        </Title>
        <Paragraph className="text-xl text-gray-600 max-w-3xl mx-auto">
          专业的销售团队和技术支持，为您提供一对一的咨询服务
        </Paragraph>
      </div>

      <Row gutter={[32, 32]}>
        {/* 左侧：咨询表单 */}
        <Col xs={24} lg={14}>
          <Card className="shadow-lg">
            <div className="mb-6">
              <Title level={3} className="text-gray-900 mb-2 flex items-center">
                <RocketOutlined className="mr-3 text-blue-600" />
                申请产品演示
              </Title>
              <Paragraph className="text-gray-600">
                填写以下信息，我们将为您安排专业的产品演示和方案介绍
              </Paragraph>
            </div>

            <Form
              form={form}
              layout="vertical"
              onFinish={handleSubmit}
              className="space-y-4"
            >
              <Row gutter={[16, 0]}>
                <Col xs={24} sm={12}>
                  <Form.Item
                    name="name"
                    label="姓名"
                    rules={[{ required: true, message: '请输入您的姓名' }]}
                  >
                    <Input placeholder="请输入您的姓名" size="large" />
                  </Form.Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Form.Item
                    name="position"
                    label="职位"
                    rules={[{ required: true, message: '请输入您的职位' }]}
                  >
                    <Input placeholder="如：技术总监、CTO等" size="large" />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={[16, 0]}>
                <Col xs={24} sm={12}>
                  <Form.Item
                    name="company"
                    label="公司名称"
                    rules={[{ required: true, message: '请输入公司名称' }]}
                  >
                    <Input placeholder="请输入公司名称" size="large" />
                  </Form.Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Form.Item
                    name="industry"
                    label="所属行业"
                    rules={[{ required: true, message: '请选择所属行业' }]}
                  >
                    <Select placeholder="请选择所属行业" size="large">
                      <Option value="finance">金融科技</Option>
                      <Option value="ecommerce">电子商务</Option>
                      <Option value="saas">企业服务</Option>
                      <Option value="data">数据服务</Option>
                      <Option value="education">教育培训</Option>
                      <Option value="healthcare">医疗健康</Option>
                      <Option value="other">其他</Option>
                    </Select>
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={[16, 0]}>
                <Col xs={24} sm={12}>
                  <Form.Item
                    name="phone"
                    label="联系电话"
                    rules={[
                      { required: true, message: '请输入联系电话' },
                      { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码' }
                    ]}
                  >
                    <Input placeholder="请输入手机号码" size="large" />
                  </Form.Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Form.Item
                    name="email"
                    label="邮箱地址"
                    rules={[
                      { required: true, message: '请输入邮箱地址' },
                      { type: 'email', message: '请输入正确的邮箱地址' }
                    ]}
                  >
                    <Input placeholder="请输入邮箱地址" size="large" />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={[16, 0]}>
                <Col xs={24} sm={12}>
                  <Form.Item
                    name="scale"
                    label="企业规模"
                    rules={[{ required: true, message: '请选择企业规模' }]}
                  >
                    <Select placeholder="请选择企业规模" size="large">
                      <Option value="startup">初创企业（&lt;50人）</Option>
                      <Option value="small">小型企业（50-200人）</Option>
                      <Option value="medium">中型企业（200-1000人）</Option>
                      <Option value="large">大型企业（1000+人）</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Form.Item
                    name="budget"
                    label="预算范围"
                  >
                    <Select placeholder="请选择预算范围（可选）" size="large">
                      <Option value="10w-50w">10万-50万</Option>
                      <Option value="50w-100w">50万-100万</Option>
                      <Option value="100w+">100万以上</Option>
                      <Option value="negotiable">面议</Option>
                    </Select>
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item
                name="requirements"
                label="具体需求"
                rules={[{ required: true, message: '请描述您的具体需求' }]}
              >
                <TextArea
                  rows={4}
                  placeholder="请详细描述您的业务场景、功能需求、预期目标等"
                  size="large"
                />
              </Form.Item>

              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  size="large"
                  loading={loading}
                  className="w-full bg-blue-600 hover:bg-blue-700 border-blue-600 hover:border-blue-700 h-12 text-base font-semibold"
                >
                  提交申请
                </Button>
              </Form.Item>
            </Form>
          </Card>
        </Col>

        {/* 右侧：联系方式和服务优势 */}
        <Col xs={24} lg={10}>
          <div className="space-y-6">
            {/* 联系方式 */}
            <Card className="shadow-lg">
              <Title level={4} className="text-gray-900 mb-4 flex items-center">
                <CustomerServiceOutlined className="mr-2 text-blue-600" />
                多种联系方式
              </Title>
              <div className="space-y-4">
                {contactMethods.map((method, index) => (
                  <div key={index} className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                    {method.icon}
                    <div>
                      <div className="font-semibold text-gray-900">{method.title}</div>
                      <div className="text-blue-600">{method.content}</div>
                      <div className="text-sm text-gray-600">{method.description}</div>
                    </div>
                  </div>
                ))}
              </div>
            </Card>

            {/* 服务优势 */}
            <Card className="shadow-lg">
              <Title level={4} className="text-gray-900 mb-4">
                服务优势
              </Title>
              <div className="space-y-3">
                {serviceAdvantages.map((advantage, index) => (
                  <div key={index} className="flex items-center space-x-2">
                    <CheckCircleOutlined className="text-green-500 flex-shrink-0" />
                    <span className="text-gray-700">{advantage}</span>
                  </div>
                ))}
              </div>
            </Card>

            {/* 响应时间 */}
            <Card className="shadow-lg bg-blue-50 border-blue-200">
              <div className="text-center">
                <ClockCircleOutlined className="text-3xl text-blue-600 mb-3" />
                <Title level={4} className="text-blue-900 mb-2">
                  快速响应
                </Title>
                <Paragraph className="text-blue-700 mb-0">
                  提交申请后，我们将在
                  <span className="font-bold text-blue-900"> 2小时内 </span>
                  与您取得联系
                </Paragraph>
              </div>
            </Card>
          </div>
        </Col>
      </Row>
    </div>
  );
};

export default SalesContact;
