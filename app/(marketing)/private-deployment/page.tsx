
import { Metadata } from "next";
import { getSystemConfig } from "@/app/actions";
import { SiteConfig } from "@/app/types";
import PrivateDeploymentHero from "@/components/private-deployment/hero";
import PrivateDeploymentFeatures from "@/components/private-deployment/features";
import PrivateDeploymentSolutions from "@/components/private-deployment/solutions";
import PrivateDeploymentArchitecture from "@/components/private-deployment/architecture";
import PrivateDeploymentCases from "@/components/private-deployment/cases";
import PrivateDeploymentContact from "@/components/private-deployment/contact";

// 页面数据接口
interface PrivateDeploymentPageData {
  title: string;
  description: string;
  keywords: string[];
}

// 生成动态 SEO metadata
export async function generateMetadata(): Promise<Metadata> {
  const siteConfig = await getSystemConfig() as SiteConfig;
  
  const pageData: PrivateDeploymentPageData = {
    title: `私有化部署解决方案 - ${siteConfig.site_name}`,
    description: `${siteConfig.site_name}提供专业的API管理系统私有化部署解决方案，支持企业级安全、定制化开发、本地化部署，为您的业务提供稳定可靠的API服务平台。`,
    keywords: ['私有化部署', 'API管理系统', '企业级解决方案', '本地化部署', '定制化开发', 'API服务平台']
  };

  return {
    title: pageData.title,
    description: pageData.description,
    keywords: pageData.keywords.join(', '),
    openGraph: {
      title: pageData.title,
      description: pageData.description,
      type: 'website',
      siteName: siteConfig.site_name,
      images: [
        {
          url: '/images/private-deployment-og.jpg',
          width: 1200,
          height: 630,
          alt: pageData.title,
        }
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title: pageData.title,
      description: pageData.description,
      images: ['/images/private-deployment-og.jpg'],
    },
    alternates: {
      canonical: '/private-deployment',
    },
  };
}

export default async function PrivateDeploymentPage() {
  const siteConfig = await getSystemConfig() as SiteConfig;

  return (
    <div className="w-full max-w-6xl mx-auto">
      {/* Hero 区域 */}
      <PrivateDeploymentHero siteConfig={siteConfig} />
      
      {/* 产品特性 */}
      <PrivateDeploymentFeatures />
      
      {/* 解决方案 */}
      <PrivateDeploymentSolutions />
      
      {/* 技术架构 */}
      <PrivateDeploymentArchitecture />
      
      {/* 客户案例 */}
      <PrivateDeploymentCases />
      
      {/* 联系方式 */}
      <PrivateDeploymentContact siteConfig={siteConfig} />
    </div>
  );
}
