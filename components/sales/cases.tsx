"use client";

import React from "react";
import { Card, Row, Col, Typography, Avatar, Tag } from "antd";
import {
  BankOutlined,
  ShopOutlined,
  GlobalOutlined,
  BuildOutlined,
  TeamOutlined,
  TrophyOutlined
} from "@ant-design/icons";

const { Title, Paragraph } = Typography;

interface CaseStudy {
  id: string;
  companyName: string;
  industry: string;
  icon: React.ReactNode;
  description: string;
  challenge: string;
  solution: string;
  results: string[];
  testimonial: {
    content: string;
    author: string;
    position: string;
  };
  tags: string[];
  metrics: {
    label: string;
    value: string;
    improvement: string;
  }[];
}

const caseStudies: CaseStudy[] = [
  {
    id: "fintech",
    companyName: "某金融科技公司",
    industry: "金融科技",
    icon: <BankOutlined className="text-2xl text-blue-600" />,
    description: "专注于为中小企业提供金融服务的科技公司",
    challenge: "需要快速构建API商业化平台，实现金融数据接口的销售和管理",
    solution: "部署完整的API销售管理系统，实现金融数据接口的商业化运营",
    results: [
      "3个月内上线50+金融API接口",
      "月收入突破100万元",
      "客户满意度达到95%+",
      "API调用成功率99.9%"
    ],
    testimonial: {
      content: "这套系统帮助我们快速实现了API商业化，从部署到盈利只用了3个月时间，效果超出预期。",
      author: "张总",
      position: "技术总监"
    },
    tags: ["金融科技", "API商业化", "快速盈利"],
    metrics: [
      { label: "月收入", value: "100万+", improvement: "+500%" },
      { label: "API数量", value: "50+", improvement: "从0开始" },
      { label: "客户数量", value: "1000+", improvement: "+1000%" }
    ]
  },
  {
    id: "ecommerce",
    companyName: "某电商服务商",
    industry: "电子商务",
    icon: <ShopOutlined className="text-2xl text-green-600" />,
    description: "为电商企业提供数据分析和营销工具的服务商",
    challenge: "原有API服务分散，缺乏统一的商业化管理平台",
    solution: "使用API销售管理系统整合所有服务，建立统一的商业化平台",
    results: [
      "整合20+电商API服务",
      "客户转化率提升60%",
      "运营效率提升80%",
      "年收入增长200%"
    ],
    testimonial: {
      content: "统一的API管理平台大大提升了我们的运营效率，客户体验也得到显著改善。",
      author: "李经理",
      position: "产品总监"
    },
    tags: ["电商", "数据分析", "营销工具"],
    metrics: [
      { label: "API整合", value: "20+", improvement: "统一管理" },
      { label: "转化率", value: "60%", improvement: "+60%" },
      { label: "年收入", value: "增长200%", improvement: "+200%" }
    ]
  },
  {
    id: "saas",
    companyName: "某SaaS平台",
    industry: "企业服务",
    icon: <GlobalOutlined className="text-2xl text-purple-600" />,
    description: "为企业提供一站式数字化解决方案的SaaS平台",
    challenge: "需要为企业客户提供开放API，实现平台生态建设",
    solution: "基于API销售管理系统构建开放平台，支持第三方开发者接入",
    results: [
      "开放100+企业API接口",
      "吸引500+第三方开发者",
      "平台生态收入占比30%",
      "客户粘性提升50%"
    ],
    testimonial: {
      content: "开放平台的建设为我们带来了新的收入增长点，也增强了客户粘性。",
      author: "王总",
      position: "CEO"
    },
    tags: ["SaaS", "开放平台", "生态建设"],
    metrics: [
      { label: "开放API", value: "100+", improvement: "全新业务" },
      { label: "开发者", value: "500+", improvement: "生态建设" },
      { label: "生态收入", value: "30%", improvement: "新增长点" }
    ]
  },
  {
    id: "startup",
    companyName: "某创业公司",
    industry: "数据服务",
    icon: <BuildOutlined className="text-2xl text-orange-600" />,
    description: "专注于提供行业数据分析服务的创业公司",
    challenge: "作为初创公司，需要快速搭建API商业化平台验证商业模式",
    solution: "采用API销售管理系统快速上线，专注于核心数据服务开发",
    results: [
      "2周内完成平台搭建",
      "快速验证商业模式",
      "获得天使轮融资",
      "月活跃用户1000+"
    ],
    testimonial: {
      content: "这套系统让我们能够专注于核心业务，快速验证了商业模式，为融资提供了有力支撑。",
      author: "陈总",
      position: "创始人"
    },
    tags: ["创业公司", "快速验证", "数据服务"],
    metrics: [
      { label: "搭建时间", value: "2周", improvement: "极速上线" },
      { label: "融资", value: "天使轮", improvement: "商业验证" },
      { label: "月活用户", value: "1000+", improvement: "快速增长" }
    ]
  }
];

const SalesCases: React.FC = () => {
  return (
    <div className="w-full py-20 px-4 lg:px-8 xl:px-16 bg-gray-50">
      {/* 标题区域 */}
      <div className="text-center mb-16">
        <Title level={2} className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
          客户成功案例
        </Title>
        <Paragraph className="text-xl text-gray-600 max-w-3xl mx-auto">
          已为500+企业客户提供API销售管理系统，覆盖金融、电商、SaaS、数据服务等多个行业
        </Paragraph>
      </div>

      {/* 案例展示 */}
      <Row gutter={[24, 24]}>
        {caseStudies.map((caseStudy, index) => (
          <Col xs={24} lg={12} key={caseStudy.id}>
            <Card className="h-full hover:shadow-xl transition-shadow duration-300">
              {/* 公司信息 */}
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center mr-4">
                  {caseStudy.icon}
                </div>
                <div>
                  <Title level={5} className="mb-1 text-gray-900">
                    {caseStudy.companyName}
                  </Title>
                  <div className="text-sm text-gray-600">{caseStudy.industry}</div>
                </div>
              </div>

              {/* 标签 */}
              <div className="mb-4">
                {caseStudy.tags.map((tag, tagIndex) => (
                  <Tag key={tagIndex} color="blue-inverse" className="mb-1 mr-1">
                    {tag}
                  </Tag>
                ))}
              </div>

              {/* 公司描述 */}
              <Paragraph className="text-gray-600 mb-4">
                {caseStudy.description}
              </Paragraph>

              {/* 挑战与解决方案 */}
              <div className="mb-4">
                <div className="mb-3">
                  <div className="text-sm font-semibold text-gray-900 mb-1">面临挑战</div>
                  <div className="text-sm text-gray-600">{caseStudy.challenge}</div>
                </div>
                <div className="mb-3">
                  <div className="text-sm font-semibold text-gray-900 mb-1">解决方案</div>
                  <div className="text-sm text-gray-600">{caseStudy.solution}</div>
                </div>
              </div>

              {/* 关键指标 */}
              <div className="mb-4">
                <div className="text-sm font-semibold text-gray-900 mb-2">关键指标</div>
                <Row gutter={[8, 8]}>
                  {caseStudy.metrics.map((metric, metricIndex) => (
                    <Col xs={24} sm={8} key={metricIndex}>
                      <div className="text-center p-2 bg-blue-50 rounded">
                        <div className="text-lg font-bold text-blue-600">{metric.value}</div>
                        <div className="text-xs text-gray-600">{metric.label}</div>
                        <div className="text-xs text-green-600">{metric.improvement}</div>
                      </div>
                    </Col>
                  ))}
                </Row>
              </div>

              {/* 客户评价 */}
              <div className="border-t pt-4">
                <div className="flex items-start space-x-3">
                  <Avatar 
                    size={40}
                    className="flex-shrink-0 bg-blue-100 text-blue-600"
                    icon={<TeamOutlined />}
                  />
                  <div className="flex-1">
                    <Paragraph className="text-sm text-gray-700 mb-2 italic">
                      "{caseStudy.testimonial.content}"
                    </Paragraph>
                    <div className="text-xs text-gray-600">
                      <span className="font-medium">{caseStudy.testimonial.author}</span>
                      <span className="mx-1">·</span>
                      <span>{caseStudy.testimonial.position}</span>
                    </div>
                  </div>
                </div>
              </div>
            </Card>
          </Col>
        ))}
      </Row>

      {/* 成功数据统计 */}
      <div className="mt-16 text-center">
        <Title level={3} className="text-2xl font-bold text-gray-900 mb-8">
          客户成功数据
        </Title>
        <Row gutter={[24, 24]}>
          <Col xs={12} sm={6}>
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-600 mb-2">500+</div>
              <div className="text-gray-600">企业客户</div>
            </div>
          </Col>
          <Col xs={12} sm={6}>
            <div className="text-center">
              <div className="text-3xl font-bold text-green-600 mb-2">95%</div>
              <div className="text-gray-600">客户满意度</div>
            </div>
          </Col>
          <Col xs={12} sm={6}>
            <div className="text-center">
              <div className="text-3xl font-bold text-purple-600 mb-2">10亿+</div>
              <div className="text-gray-600">年API调用量</div>
            </div>
          </Col>
          <Col xs={12} sm={6}>
            <div className="text-center">
              <div className="text-3xl font-bold text-orange-600 mb-2">99.9%</div>
              <div className="text-gray-600">系统可用性</div>
            </div>
          </Col>
        </Row>
      </div>
    </div>
  );
};

export default SalesCases;
