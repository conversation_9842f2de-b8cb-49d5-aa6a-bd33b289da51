import React from "react";
import { Metadata } from "next";
import { getSystemConfig } from "@/app/actions";
import { SiteConfig } from "@/app/types";
import { Typography, Card, Row, Col } from "antd";
import { 
  CloudServerOutlined, 
  SafetyCertificateOutlined, 
  SettingOutlined 
} from "@ant-design/icons";

const { Title, Paragraph } = Typography;

// 生成动态 SEO metadata
export async function generateMetadata(): Promise<Metadata> {
  const siteConfig = await getSystemConfig() as SiteConfig;
  
  return {
    title: `私有化部署解决方案测试 - ${siteConfig.site_name}`,
    description: `${siteConfig.site_name}提供专业的API管理系统私有化部署解决方案测试页面。`,
  };
}

export default async function PrivateDeploymentTestPage() {
  const siteConfig = await getSystemConfig() as SiteConfig;

  return (
    <div className="w-full max-w-6xl mx-auto py-20 px-4 lg:px-0">
      {/* Hero 区域 */}
      <div className="text-center mb-16">
        <Title level={1} className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
          私有化部署解决方案
          <br />
          <span className="text-blue-600">测试页面</span>
        </Title>
        
        <Paragraph className="text-xl text-gray-600 mb-8">
          为企业提供安全、稳定、可定制的API管理平台，支持本地化部署，
          满足数据安全合规要求，助力企业数字化转型。
        </Paragraph>
      </div>

      {/* 特性展示 */}
      <div className="mb-16">
        <Title level={2} className="text-center text-3xl font-bold text-gray-900 mb-8">
          核心特性
        </Title>
        
        <Row gutter={[24, 24]}>
          <Col xs={24} sm={8}>
            <Card className="text-center h-full">
              <SafetyCertificateOutlined className="text-4xl text-blue-600 mb-4" />
              <Title level={4}>企业级安全</Title>
              <Paragraph>多层次安全防护体系，确保数据安全和系统稳定</Paragraph>
            </Card>
          </Col>
          
          <Col xs={24} sm={8}>
            <Card className="text-center h-full">
              <CloudServerOutlined className="text-4xl text-blue-600 mb-4" />
              <Title level={4}>灵活部署</Title>
              <Paragraph>支持多种部署环境，满足不同企业需求</Paragraph>
            </Card>
          </Col>
          
          <Col xs={24} sm={8}>
            <Card className="text-center h-full">
              <SettingOutlined className="text-4xl text-blue-600 mb-4" />
              <Title level={4}>深度定制</Title>
              <Paragraph>根据企业业务需求进行个性化定制开发</Paragraph>
            </Card>
          </Col>
        </Row>
      </div>

      {/* 联系信息 */}
      <div className="text-center bg-blue-50 rounded-lg p-8">
        <Title level={3} className="text-gray-900 mb-4">
          了解更多信息
        </Title>
        <Paragraph className="text-gray-600 mb-6">
          如需了解详细的私有化部署方案，请联系我们的专业团队
        </Paragraph>
        <div className="text-blue-600 font-semibold">
          联系电话：400-123-4567
        </div>
      </div>
    </div>
  );
}
