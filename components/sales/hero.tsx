"use client";

import React from "react";
import { Button, Space, Typography, Row, Col } from "antd";
import { SiteConfig } from "@/app/types";
import { 
  ApiOutlined, 
  DollarOutlined, 
  TeamOutlined,
  RocketOutlined,
  CheckCircleOutlined,
  PlayCircleOutlined
} from "@ant-design/icons";
import Link from "next/link";

const { Title, Paragraph } = Typography;

interface SalesHeroProps {
  siteConfig: SiteConfig;
}

const SalesHero: React.FC<SalesHeroProps> = ({ siteConfig }) => {
  const handleDemoClick = () => {
    // 滚动到联系表单区域
    const contactSection = document.getElementById('contact-section');
    if (contactSection) {
      contactSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const coreValues = [
    {
      icon: <ApiOutlined className="text-2xl text-blue-600" />,
      title: "简单快速",
      description: "API接口发布和销售"
    },
    {
      icon: <DollarOutlined className="text-2xl text-blue-600" />,
      title: "完整商业化",
      description: "支付、工单、发票一体化"
    },
    {
      icon: <TeamOutlined className="text-2xl text-blue-600" />,
      title: "VIP会员体系",
      description: "多套餐、多权限管理"
    }
  ];

  const highlights = [
    "✅ 11个核心商业化功能模块",
    "✅ 高性能技术架构支撑",
    "✅ 完整的API生命周期管理",
    "✅ 多种接口类型和计费模式",
    "✅ 专业的SDK和文档生成",
    "✅ 7x24小时技术支持"
  ];

  return (
    <div className="relative bg-gradient-to-br from-blue-50 via-white to-blue-50 overflow-hidden">
      {/* 背景装饰元素 */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-10 w-32 h-32 bg-blue-100 rounded-full opacity-20"></div>
        <div className="absolute top-40 right-20 w-24 h-24 bg-blue-200 rounded-full opacity-30"></div>
        <div className="absolute bottom-20 left-1/3 w-40 h-40 bg-blue-50 rounded-full opacity-25"></div>
      </div>

      <div className="relative w-full max-w-6xl mx-auto py-20 lg:py-32 px-4 lg:px-0">
        <Row gutter={[48, 48]} align="middle">
          {/* 左侧内容 */}
          <Col xs={24} lg={14}>
            <div className="text-center lg:text-left">
              <div className="mb-6">
                <span className="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                  <RocketOutlined className="mr-2" />
                  商业API销售管理系统
                </span>
              </div>

              <Title level={1} className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6 leading-tight">
                专业的API商业化
                <br />
                <span className="text-blue-600">销售管理平台</span>
              </Title>

              <Paragraph className="text-xl text-gray-600 mb-8 leading-relaxed">
                集成API发布、销售、支付、管理于一体的完整商业化解决方案。
                支持多种计费模式、VIP会员体系、工单管理等11个核心功能模块，
                助力企业快速构建API商业生态。
              </Paragraph>

              {/* 核心价值点 */}
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 mb-8">
                {coreValues.map((value, index) => (
                  <div key={index} className="flex items-center space-x-3 p-4 bg-white rounded-lg shadow-sm">
                    {value.icon}
                    <div>
                      <div className="font-semibold text-gray-900">{value.title}</div>
                      <div className="text-sm text-gray-600">{value.description}</div>
                    </div>
                  </div>
                ))}
              </div>

              {/* CTA 按钮组 */}
              <Space size="large" className="flex flex-col sm:flex-row justify-center lg:justify-start mb-8">
                <Link href="/vip">
                  <Button 
                    type="primary" 
                    size="large" 
                    className="bg-blue-600 hover:bg-blue-700 border-blue-600 hover:border-blue-700 px-8 py-3 h-auto text-base font-semibold"
                    icon={<RocketOutlined />}
                  >
                    立即购买
                  </Button>
                </Link>
                <Button 
                  size="large" 
                  className="border-blue-600 text-blue-600 hover:border-blue-700 hover:text-blue-700 px-8 py-3 h-auto text-base font-semibold"
                  icon={<PlayCircleOutlined />}
                  onClick={handleDemoClick}
                >
                  申请演示
                </Button>
              </Space>

              {/* 产品亮点 */}
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 text-sm">
                {highlights.map((highlight, index) => (
                  <div key={index} className="flex items-center text-gray-700">
                    <span className="mr-2">{highlight}</span>
                  </div>
                ))}
              </div>
            </div>
          </Col>

          {/* 右侧图片/演示 */}
          <Col xs={24} lg={10}>
            <div className="relative">
              <div className="relative z-10">
                <div className="w-full h-80 bg-gradient-to-br from-blue-100 to-blue-200 rounded-lg shadow-2xl flex items-center justify-center">
                  <div className="text-center">
                    <ApiOutlined className="text-6xl text-blue-600 mb-4" />
                    <div className="text-lg font-semibold text-blue-800">API销售管理系统</div>
                    <div className="text-sm text-blue-600 mt-2">商业化 · 专业 · 高效</div>
                  </div>
                </div>
              </div>
              
              {/* 浮动卡片装饰 */}
              <div className="absolute -top-4 -right-4 bg-white rounded-lg shadow-lg p-4 z-20">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span className="text-sm font-medium text-gray-700">系统运行正常</span>
                </div>
              </div>
              
              <div className="absolute -bottom-4 -left-4 bg-white rounded-lg shadow-lg p-4 z-20">
                <div className="text-center">
                  <div className="text-lg font-bold text-blue-600">500+</div>
                  <div className="text-xs text-gray-600">企业客户</div>
                </div>
              </div>
            </div>
          </Col>
        </Row>

        {/* 信任指标 */}
        <div className="mt-16 pt-8 border-t border-gray-200">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
            <div>
              <div className="text-3xl font-bold text-blue-600">500+</div>
              <div className="text-sm text-gray-600">企业客户</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-blue-600">10亿+</div>
              <div className="text-sm text-gray-600">年API调用量</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-blue-600">99.9%</div>
              <div className="text-sm text-gray-600">服务可用性</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-blue-600">24/7</div>
              <div className="text-sm text-gray-600">技术支持</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SalesHero;
