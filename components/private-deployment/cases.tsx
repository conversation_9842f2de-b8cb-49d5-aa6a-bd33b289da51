"use client";

import React from "react";
import { Card, Row, Col, Typography, Avatar, Rate, Tag } from "antd";
import {
  BankOutlined,
  ShopOutlined,
  MedicineBoxOutlined,
  CarOutlined,
  GlobalOutlined,
  BuildOutlined
} from "@ant-design/icons";
import Image from "next/image";

const { Title, Paragraph } = Typography;

interface CaseStudy {
  id: string;
  companyName: string;
  industry: string;
  logo: string;
  icon: React.ReactNode;
  description: string;
  challenge: string;
  solution: string;
  results: string[];
  testimonial: {
    content: string;
    author: string;
    position: string;
    avatar: string;
  };
  tags: string[];
  metrics: {
    label: string;
    value: string;
    improvement: string;
  }[];
}

const caseStudies: CaseStudy[] = [
  {
    id: "fintech",
    companyName: "某大型银行",
    industry: "金融科技",
    logo: "/images/cases/bank-logo.png",
    icon: <BankOutlined className="text-2xl text-blue-600" />,
    description: "国内领先的股份制商业银行，拥有数千万客户",
    challenge: "原有API管理系统无法满足快速增长的业务需求，存在安全隐患和性能瓶颈",
    solution: "部署企业级私有化API管理平台，实现统一的API生命周期管理和安全防护",
    results: [
      "API响应时间提升60%",
      "系统可用性达到99.99%",
      "开发效率提升40%",
      "安全事件降低90%"
    ],
    testimonial: {
      content: "私有化部署方案完美解决了我们的数据安全和合规要求，系统稳定性和性能都有显著提升。",
      author: "张总",
      position: "技术总监",
      avatar: ""
    },
    tags: ["金融", "高并发", "安全合规"],
    metrics: [
      { label: "API调用量", value: "10亿+/月", improvement: "+200%" },
      { label: "响应时间", value: "<50ms", improvement: "-60%" },
      { label: "系统可用性", value: "99.99%", improvement: "+0.5%" }
    ]
  },
  {
    id: "ecommerce",
    companyName: "某电商平台",
    industry: "电子商务",
    logo: "/images/cases/ecommerce-logo.png",
    icon: <ShopOutlined className="text-2xl text-green-600" />,
    description: "国内知名B2B电商平台，服务数万家企业客户",
    challenge: "多系统集成复杂，API管理分散，缺乏统一的开发者门户",
    solution: "构建统一的API管理平台，提供开发者门户和API商店功能",
    results: [
      "集成效率提升50%",
      "开发者满意度95%+",
      "API复用率提升80%",
      "运维成本降低30%"
    ],
    testimonial: {
      content: "统一的API管理平台大大简化了我们的系统集成工作，开发者体验得到显著改善。",
      author: "李经理",
      position: "产品总监",
      avatar: ""
    },
    tags: ["电商", "开放平台", "开发者生态"],
    metrics: [
      { label: "接入商家", value: "5000+", improvement: "+150%" },
      { label: "API数量", value: "500+", improvement: "+300%" },
      { label: "集成时间", value: "2天", improvement: "-75%" }
    ]
  },
  {
    id: "healthcare",
    companyName: "某医疗集团",
    industry: "医疗健康",
    logo: "/images/cases/healthcare-logo.png",
    icon: <MedicineBoxOutlined className="text-2xl text-red-600" />,
    description: "大型医疗集团，拥有多家医院和医疗机构",
    challenge: "医疗数据敏感，需要严格的数据安全和隐私保护",
    solution: "部署符合医疗行业标准的私有化API管理系统",
    results: [
      "通过HIPAA合规认证",
      "数据泄露风险降低95%",
      "医疗系统集成效率提升70%",
      "患者数据访问速度提升3倍"
    ],
    testimonial: {
      content: "系统的安全性和合规性完全满足医疗行业的严格要求，为我们的数字化转型提供了坚实基础。",
      author: "王院长",
      position: "信息化负责人",
      avatar: ""
    },
    tags: ["医疗", "数据安全", "合规认证"],
    metrics: [
      { label: "医疗机构", value: "50+", improvement: "+100%" },
      { label: "患者数据", value: "100万+", improvement: "安全保护" },
      { label: "系统集成", value: "20+", improvement: "+400%" }
    ]
  },
  {
    id: "automotive",
    companyName: "某汽车制造商",
    industry: "汽车制造",
    logo: "/images/cases/auto-logo.png",
    icon: <CarOutlined className="text-2xl text-purple-600" />,
    description: "知名汽车制造企业，正在向智能网联汽车转型",
    challenge: "车联网数据量巨大，需要高性能的API管理和实时数据处理",
    solution: "部署高性能私有化API管理平台，支持车联网数据实时处理",
    results: [
      "支持千万级车辆接入",
      "实时数据处理延迟<10ms",
      "系统扩展性提升500%",
      "运营成本降低40%"
    ],
    testimonial: {
      content: "高性能的API管理平台为我们的车联网业务提供了强有力的技术支撑，系统稳定可靠。",
      author: "陈总工",
      position: "技术总工程师",
      avatar: ""
    },
    tags: ["车联网", "高性能", "实时处理"],
    metrics: [
      { label: "车辆接入", value: "1000万+", improvement: "+1000%" },
      { label: "数据处理", value: "TB级/天", improvement: "+800%" },
      { label: "响应延迟", value: "<10ms", improvement: "-80%" }
    ]
  }
];

const PrivateDeploymentCases: React.FC = () => {
  return (
    <div className="w-full max-w-6xl mx-auto py-20 px-4 lg:px-0 bg-gray-50">
      {/* 标题区域 */}
      <div className="text-center mb-16">
        <Title level={2} className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
          客户成功案例
        </Title>
        <Paragraph className="text-xl text-gray-600 max-w-3xl mx-auto">
          已为500+企业客户提供私有化部署服务，覆盖金融、电商、医疗、制造等多个行业
        </Paragraph>
      </div>

      {/* 案例展示 */}
      <Row gutter={[24, 24]}>
        {caseStudies.map((caseStudy, index) => (
          <Col xs={24} lg={12} key={caseStudy.id}>
            <Card className="h-full hover:shadow-xl transition-shadow duration-300">
              {/* 公司信息 */}
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center mr-4">
                  {caseStudy.icon}
                </div>
                <div>
                  <Title level={5} className="mb-1 text-gray-900">
                    {caseStudy.companyName}
                  </Title>
                  <div className="text-sm text-gray-600">{caseStudy.industry}</div>
                </div>
              </div>

              {/* 标签 */}
              <div className="mb-4">
                {caseStudy.tags.map((tag, tagIndex) => (
                  <Tag key={tagIndex} color="blue-inverse" className="mb-1 mr-1">
                    {tag}
                  </Tag>
                ))}
              </div>

              {/* 公司描述 */}
              <Paragraph className="text-gray-600 mb-4">
                {caseStudy.description}
              </Paragraph>

              {/* 挑战与解决方案 */}
              <div className="mb-4">
                <div className="mb-3">
                  <div className="text-sm font-semibold text-gray-900 mb-1">面临挑战</div>
                  <div className="text-sm text-gray-600">{caseStudy.challenge}</div>
                </div>
                <div className="mb-3">
                  <div className="text-sm font-semibold text-gray-900 mb-1">解决方案</div>
                  <div className="text-sm text-gray-600">{caseStudy.solution}</div>
                </div>
              </div>

              {/* 关键指标 */}
              <div className="mb-4">
                <div className="text-sm font-semibold text-gray-900 mb-2">关键指标</div>
                <Row gutter={[8, 8]}>
                  {caseStudy.metrics.map((metric, metricIndex) => (
                    <Col xs={24} sm={8} key={metricIndex}>
                      <div className="text-center p-2 bg-blue-50 rounded">
                        <div className="text-lg font-bold text-blue-600">{metric.value}</div>
                        <div className="text-xs text-gray-600">{metric.label}</div>
                        <div className="text-xs text-green-600">{metric.improvement}</div>
                      </div>
                    </Col>
                  ))}
                </Row>
              </div>

              {/* 客户评价 */}
              <div className="border-t pt-4">
                <div className="flex items-start space-x-3">
                  <Avatar
                    size={40}
                    className="flex-shrink-0 bg-blue-100 text-blue-600"
                    // icon={<TeamOutlined />}
                  />
                  <div className="flex-1">
                    <Paragraph className="text-sm text-gray-700 mb-2 italic">
                      "{caseStudy.testimonial.content}"
                    </Paragraph>
                    <div className="text-xs text-gray-600">
                      <span className="font-medium">{caseStudy.testimonial.author}</span>
                      <span className="mx-1">·</span>
                      <span>{caseStudy.testimonial.position}</span>
                    </div>
                  </div>
                </div>
              </div>
            </Card>
          </Col>
        ))}
      </Row>

      {/* 合作伙伴展示 */}
      <div className="mt-16 text-center">
        <Title level={4} className="text-gray-900 mb-8">
          信赖我们的企业客户
        </Title>
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-8 items-center opacity-60">
          {[
            { name: "某银行", icon: <BankOutlined /> },
            { name: "某电商", icon: <ShopOutlined /> },
            { name: "某医院", icon: <MedicineBoxOutlined /> },
            { name: "某车企", icon: <CarOutlined /> },
            { name: "某集团", icon: <GlobalOutlined /> },
            { name: "某公司", icon: <BuildOutlined /> }
          ].map((partner, i) => (
            <div key={i} className="flex flex-col items-center justify-center h-16 bg-gray-100 rounded-lg hover:bg-gray-200 transition-all duration-300">
              <div className="text-2xl text-gray-500 mb-1">{partner.icon}</div>
              <div className="text-xs text-gray-600">{partner.name}</div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default PrivateDeploymentCases;
