"use client";

import React from "react";
import { Card, Row, Col, Typography, Badge } from "antd";
import {
  ApiOutlined,
  DollarOutlined,
  CustomerServiceOutlined,
  FileTextOutlined,
  TeamOutlined,
  GiftOutlined,
  BarChartOutlined,
  BellOutlined,
  SafetyCertificateOutlined,
  CodeOutlined,
  SettingOutlined
} from "@ant-design/icons";

const { Title, Paragraph } = Typography;

interface FeatureItem {
  id: number;
  icon: React.ReactNode;
  title: string;
  description: string;
  highlights: string[];
  category: 'core' | 'business' | 'management';
}

const features: FeatureItem[] = [
  {
    id: 1,
    icon: <ApiOutlined className="text-3xl text-blue-600" />,
    title: "API接口发布销售",
    description: "简单快速的API接口发布和销售管理",
    highlights: ["一键发布API", "多种销售模式", "实时销售统计", "接口版本管理"],
    category: 'core'
  },
  {
    id: 2,
    icon: <SettingOutlined className="text-3xl text-blue-600" />,
    title: "API生命周期管理",
    description: "从设计到退役的完整API管理流程",
    highlights: ["设计阶段管理", "开发测试", "发布上线", "维护退役"],
    category: 'core'
  },
  {
    id: 3,
    icon: <CodeOutlined className="text-3xl text-blue-600" />,
    title: "在线测试工具",
    description: "强大的API接口在线测试和调试功能",
    highlights: ["可视化测试", "参数验证", "响应预览", "错误诊断"],
    category: 'core'
  },
  {
    id: 4,
    icon: <FileTextOutlined className="text-3xl text-blue-600" />,
    title: "SDK代码生成",
    description: "自动生成多语言SDK代码和API文档",
    highlights: ["多语言支持", "自动生成文档", "代码示例", "版本同步"],
    category: 'core'
  },
  {
    id: 5,
    icon: <DollarOutlined className="text-3xl text-blue-600" />,
    title: "在线支付系统",
    description: "集成微信支付、支付宝等多种支付方式",
    highlights: ["微信支付", "支付宝", "银行卡支付", "自动到账"],
    category: 'business'
  },
  {
    id: 6,
    icon: <CustomerServiceOutlined className="text-3xl text-blue-600" />,
    title: "工单管理系统",
    description: "完善的客户服务和技术支持工单系统",
    highlights: ["工单分类", "优先级管理", "自动分配", "SLA监控"],
    category: 'business'
  },
  {
    id: 7,
    icon: <FileTextOutlined className="text-3xl text-blue-600" />,
    title: "发票管理系统",
    description: "专业的发票申请、开具和管理功能",
    highlights: ["在线申请", "自动开具", "电子发票", "发票查询"],
    category: 'business'
  },
  {
    id: 8,
    icon: <TeamOutlined className="text-3xl text-blue-600" />,
    title: "推广返利系统",
    description: "多级推广返利和分销管理系统",
    highlights: ["多级返利", "实时结算", "推广统计", "佣金管理"],
    category: 'business'
  },
  {
    id: 9,
    icon: <TeamOutlined className="text-3xl text-blue-600" />,
    title: "VIP会员体系",
    description: "灵活的会员套餐和权限管理系统",
    highlights: ["多套餐管理", "QPS限制", "接口权限", "会员特权"],
    category: 'management'
  },
  {
    id: 10,
    icon: <SafetyCertificateOutlined className="text-3xl text-blue-600" />,
    title: "Token密钥管理",
    description: "安全的API密钥和访问控制管理",
    highlights: ["IP白名单", "来源限制", "密钥轮换", "访问日志"],
    category: 'management'
  },
  {
    id: 11,
    icon: <GiftOutlined className="text-3xl text-blue-600" />,
    title: "礼品兑换系统",
    description: "会员积分兑换和营销活动管理",
    highlights: ["积分兑换", "优惠券", "营销活动", "库存管理"],
    category: 'management'
  }
];

const categoryConfig = {
  core: { name: "核心功能", color: "blue", count: 4 },
  business: { name: "商业化功能", color: "green", count: 4 },
  management: { name: "管理功能", color: "orange", count: 3 }
};

const SalesFeatures: React.FC = () => {
  const getCategoryFeatures = (category: string) => {
    return features.filter(feature => feature.category === category);
  };

  return (
    <div className="w-full max-w-6xl mx-auto py-20 px-4 lg:px-0">
      {/* 标题区域 */}
      <div className="text-center mb-16">
        <Title level={2} className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
          11个核心功能模块
        </Title>
        <Paragraph className="text-xl text-gray-600 max-w-3xl mx-auto">
          从API发布到商业化运营的完整功能体系，助力企业快速构建专业的API商业生态
        </Paragraph>
      </div>

      {/* 功能分类展示 */}
      {Object.entries(categoryConfig).map(([categoryKey, config]) => (
        <div key={categoryKey} className="mb-16">
          <div className="flex items-center justify-center mb-8">
            <Badge count={config.count} color={config.color} offset={[10, 0]}>
              <Title level={3} className="text-2xl font-bold text-gray-900 mr-4">
                {config.name}
              </Title>
            </Badge>
          </div>

          <Row gutter={[24, 24]}>
            {getCategoryFeatures(categoryKey).map((feature) => (
              <Col xs={24} sm={12} lg={6} key={feature.id}>
                <Card 
                  className="h-full hover:shadow-lg transition-all duration-300 border-gray-200 hover:border-blue-300"
                  styles={{ body: { padding: '24px' } }}
                >
                  <div className="text-center mb-4">
                    {feature.icon}
                  </div>
                  
                  <Title level={5} className="text-center text-gray-900 mb-3">
                    {feature.title}
                  </Title>
                  
                  <Paragraph className="text-gray-600 text-center mb-4 text-sm">
                    {feature.description}
                  </Paragraph>
                  
                  <div className="space-y-2">
                    {feature.highlights.map((highlight, idx) => (
                      <div key={idx} className="flex items-center text-sm text-gray-700">
                        <div className="w-1.5 h-1.5 bg-blue-600 rounded-full mr-2 flex-shrink-0"></div>
                        <span>{highlight}</span>
                      </div>
                    ))}
                  </div>
                </Card>
              </Col>
            ))}
          </Row>
        </div>
      ))}

      {/* 底部总结 */}
      <div className="mt-16 p-8 bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg">
        <div className="text-center">
          <Title level={3} className="text-gray-900 mb-4">
            完整的API商业化解决方案
          </Title>
          <Paragraph className="text-gray-700 text-lg mb-6">
            涵盖API发布、销售、支付、管理、运营的全流程功能，
            让您专注于API开发，我们负责商业化运营
          </Paragraph>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 text-center">
            <div className="p-4 bg-white rounded-lg shadow-sm">
              <div className="text-2xl font-bold text-blue-600">11</div>
              <div className="text-sm text-gray-600">核心功能模块</div>
            </div>
            <div className="p-4 bg-white rounded-lg shadow-sm">
              <div className="text-2xl font-bold text-blue-600">3</div>
              <div className="text-sm text-gray-600">接口计费类型</div>
            </div>
            <div className="p-4 bg-white rounded-lg shadow-sm">
              <div className="text-2xl font-bold text-blue-600">5+</div>
              <div className="text-sm text-gray-600">支付方式</div>
            </div>
            <div className="p-4 bg-white rounded-lg shadow-sm">
              <div className="text-2xl font-bold text-blue-600">10+</div>
              <div className="text-sm text-gray-600">SDK语言支持</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SalesFeatures;
