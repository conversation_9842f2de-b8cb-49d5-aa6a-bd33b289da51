"use client";

import React from "react";
import { Card, Row, Col, Typography, But<PERSON>, Tag, Divider } from "antd";
import { SiteConfig } from "@/app/types";
import {
  CheckCircleOutlined,
  StarOutlined,
  RocketOutlined,
  CrownOutlined,
  ApiOutlined,
  DollarOutlined,
  TeamOutlined,
  SafetyCertificateOutlined
} from "@ant-design/icons";
import Link from "next/link";

const { Title, Paragraph } = Typography;

interface SalesPricingProps {
  siteConfig: SiteConfig;
}

interface PricingFeature {
  name: string;
  included: boolean;
  description?: string;
}

interface PricingPlan {
  id: string;
  name: string;
  icon: React.ReactNode;
  description: string;
  price: string;
  period: string;
  popular?: boolean;
  features: PricingFeature[];
  cta: string;
  ctaLink: string;
}

const SalesPricing: React.FC<SalesPricingProps> = ({ siteConfig }) => {
  const pricingPlans: PricingPlan[] = [
    {
      id: "commercial",
      name: "商业版",
      icon: <CrownOutlined className="text-3xl text-blue-600" />,
      description: "完整的API销售管理系统，适合企业商业化运营",
      price: "联系咨询",
      period: "一次性购买",
      popular: true,
      features: [
        { name: "API接口发布销售", included: true, description: "无限制API发布" },
        { name: "完整API生命周期管理", included: true },
        { name: "在线API测试工具", included: true },
        { name: "多语言SDK代码生成", included: true, description: "10+编程语言" },
        { name: "自动生成API文档", included: true },
        { name: "在线支付系统", included: true, description: "微信、支付宝等" },
        { name: "工单管理系统", included: true },
        { name: "发票管理系统", included: true },
        { name: "推广返利系统", included: true, description: "多级返利" },
        { name: "VIP会员体系", included: true, description: "多套餐管理" },
        { name: "Token密钥管理", included: true, description: "IP白名单等" },
        { name: "第三方OAuth2登录", included: true, description: "微信、QQ、GitHub等" },
        { name: "礼品兑换系统", included: true },
        { name: "高性能日志分析", included: true, description: "ClickHouse支持" },
        { name: "内部消息通知", included: true },
        { name: "源代码交付", included: true, description: "完整源码" },
        { name: "技术支持", included: true, description: "1年免费技术支持" },
        { name: "部署服务", included: true, description: "协助部署上线" },
        { name: "定制开发", included: true, description: "根据需求定制" }
      ],
      cta: "立即咨询",
      ctaLink: "#contact-section"
    }
  ];

  const advantages = [
    {
      icon: <ApiOutlined className="text-2xl text-blue-600" />,
      title: "完整功能",
      description: "11个核心功能模块，覆盖API商业化全流程"
    },
    {
      icon: <DollarOutlined className="text-2xl text-green-600" />,
      title: "快速回本",
      description: "通过API销售快速收回投资成本"
    },
    {
      icon: <TeamOutlined className="text-2xl text-purple-600" />,
      title: "专业支持",
      description: "1年免费技术支持，协助部署上线"
    },
    {
      icon: <SafetyCertificateOutlined className="text-2xl text-orange-600" />,
      title: "源码交付",
      description: "完整源代码交付，支持二次开发"
    }
  ];

  const handleContactClick = () => {
    const contactSection = document.getElementById('contact-section');
    if (contactSection) {
      contactSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <div className="w-full py-20 px-4 lg:px-8 xl:px-16">
      {/* 标题区域 */}
      <div className="text-center mb-16">
        <Title level={2} className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
          定价方案
        </Title>
        <Paragraph className="text-xl text-gray-600 max-w-3xl mx-auto">
          一次性购买，终身使用。包含完整源代码和1年免费技术支持
        </Paragraph>
      </div>

      {/* 定价卡片 */}
      <Row gutter={[24, 24]} justify="center">
        {pricingPlans.map((plan) => (
          <Col xs={24} lg={16} key={plan.id}>
            <Card 
              className={`h-full relative ${
                plan.popular ? 'border-blue-500 shadow-xl' : 'border-gray-200 shadow-lg'
              }`}
              styles={{ body: { padding: '32px' } }}
            >
              {plan.popular && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <Tag color="blue" className="px-4 py-1 text-sm font-medium">
                    <StarOutlined className="mr-1" />
                    推荐方案
                  </Tag>
                </div>
              )}
              
              <div className="text-center mb-8">
                {plan.icon}
                <Title level={3} className="text-gray-900 mt-4 mb-2">
                  {plan.name}
                </Title>
                <Paragraph className="text-gray-600 mb-4">
                  {plan.description}
                </Paragraph>
                <div className="mb-4">
                  <span className="text-4xl font-bold text-blue-600">{plan.price}</span>
                  <span className="text-gray-600 ml-2">/ {plan.period}</span>
                </div>
              </div>

              <Divider />

              <div className="mb-8">
                <Title level={5} className="text-gray-900 mb-4">功能清单</Title>
                <Row gutter={[16, 8]}>
                  {plan.features.map((feature, index) => (
                    <Col xs={24} sm={12} key={index}>
                      <div className="flex items-start space-x-2 mb-2">
                        <CheckCircleOutlined 
                          className={`mt-1 flex-shrink-0 ${
                            feature.included ? 'text-green-500' : 'text-gray-300'
                          }`} 
                        />
                        <div className="flex-1">
                          <span className={`text-sm ${
                            feature.included ? 'text-gray-900' : 'text-gray-400'
                          }`}>
                            {feature.name}
                          </span>
                          {feature.description && (
                            <div className="text-xs text-gray-500 mt-1">
                              {feature.description}
                            </div>
                          )}
                        </div>
                      </div>
                    </Col>
                  ))}
                </Row>
              </div>

              <div className="text-center">
                <Button
                  type="primary"
                  size="large"
                  className="bg-blue-600 hover:bg-blue-700 border-blue-600 hover:border-blue-700 px-8 py-3 h-auto text-base font-semibold w-full sm:w-auto"
                  icon={<RocketOutlined />}
                  onClick={handleContactClick}
                >
                  {plan.cta}
                </Button>
              </div>
            </Card>
          </Col>
        ))}
      </Row>

      {/* 购买优势 */}
      <div className="mt-16">
        <Title level={3} className="text-center text-2xl font-bold text-gray-900 mb-8">
          为什么选择我们
        </Title>
        <Row gutter={[24, 24]}>
          {advantages.map((advantage, index) => (
            <Col xs={24} sm={12} lg={6} key={index}>
              <div className="text-center p-6 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300">
                {advantage.icon}
                <Title level={5} className="text-gray-900 mt-4 mb-2">
                  {advantage.title}
                </Title>
                <Paragraph className="text-gray-600 text-sm">
                  {advantage.description}
                </Paragraph>
              </div>
            </Col>
          ))}
        </Row>
      </div>

      {/* 购买说明 */}
      <div className="mt-16 p-8 bg-blue-50 rounded-lg">
        <div className="text-center">
          <Title level={4} className="text-gray-900 mb-4">
            购买说明
          </Title>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-left max-w-4xl mx-auto">
            <div>
              <Title level={5} className="text-gray-900 mb-2">交付内容</Title>
              <ul className="text-gray-700 space-y-1 text-sm">
                <li>• 完整系统源代码（前端+后端）</li>
                <li>• 数据库设计文档和初始化脚本</li>
                <li>• 部署文档和环境配置说明</li>
                <li>• 系统使用手册和管理员指南</li>
                <li>• 1年免费技术支持服务</li>
              </ul>
            </div>
            <div>
              <Title level={5} className="text-gray-900 mb-2">技术支持</Title>
              <ul className="text-gray-700 space-y-1 text-sm">
                <li>• 协助系统部署和环境配置</li>
                <li>• 提供技术咨询和问题解答</li>
                <li>• 系统bug修复和功能优化</li>
                <li>• 根据需求提供定制开发服务</li>
                <li>• 在线技术支持和远程协助</li>
              </ul>
            </div>
          </div>
          <div className="mt-6 p-4 bg-yellow-50 rounded-lg border border-yellow-200">
            <Paragraph className="text-yellow-800 mb-0 text-sm">
              <strong>注意：</strong>本系统为商业软件产品，购买后提供完整源代码。
              支持根据客户需求进行定制开发，具体价格面议。
            </Paragraph>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SalesPricing;
