"use client";

import React from "react";
import { Card, Row, Col, Typography } from "antd";
import {
  SafetyCertificateOutlined,
  CloudServerOutlined,
  SettingOutlined,
  MonitorOutlined,
  ApiOutlined,
  TeamOutlined,
  ThunderboltOutlined,
  SecurityScanOutlined,
  DatabaseOutlined,
  SyncOutlined,
  BugOutlined,
  CustomerServiceOutlined
} from "@ant-design/icons";

const { Title, Paragraph } = Typography;

interface FeatureItem {
  icon: React.ReactNode;
  title: string;
  description: string;
  highlights: string[];
}

const features: FeatureItem[] = [
  {
    icon: <SafetyCertificateOutlined className="text-3xl text-blue-600" />,
    title: "企业级安全保障",
    description: "多层次安全防护体系，确保数据安全和系统稳定",
    highlights: ["数据加密传输", "权限精细控制", "安全审计日志", "合规认证支持"]
  },
  {
    icon: <CloudServerOutlined className="text-3xl text-blue-600" />,
    title: "灵活部署方式",
    description: "支持多种部署环境，满足不同企业需求",
    highlights: ["私有云部署", "混合云架构", "容器化部署", "高可用集群"]
  },
  {
    icon: <SettingOutlined className="text-3xl text-blue-600" />,
    title: "深度定制开发",
    description: "根据企业业务需求进行个性化定制开发",
    highlights: ["UI界面定制", "业务流程定制", "第三方系统集成", "专属功能开发"]
  },
  {
    icon: <MonitorOutlined className="text-3xl text-blue-600" />,
    title: "实时监控运维",
    description: "全方位系统监控和智能运维管理",
    highlights: ["性能实时监控", "智能告警通知", "自动故障恢复", "运维数据分析"]
  },
  {
    icon: <ApiOutlined className="text-3xl text-blue-600" />,
    title: "完整API生命周期",
    description: "从设计到退役的完整API管理流程",
    highlights: ["API设计管理", "版本控制", "文档自动生成", "生命周期管理"]
  },
  {
    icon: <TeamOutlined className="text-3xl text-blue-600" />,
    title: "团队协作管理",
    description: "支持多团队协作和权限管理",
    highlights: ["角色权限管理", "团队协作工具", "审批流程", "操作日志追踪"]
  },
  {
    icon: <ThunderboltOutlined className="text-3xl text-blue-600" />,
    title: "高性能架构",
    description: "优化的系统架构，支持高并发访问",
    highlights: ["微服务架构", "负载均衡", "缓存优化", "性能调优"]
  },
  {
    icon: <SecurityScanOutlined className="text-3xl text-blue-600" />,
    title: "数据安全合规",
    description: "符合国内外数据安全法规要求",
    highlights: ["数据本地化", "隐私保护", "合规审计", "安全认证"]
  },
  {
    icon: <DatabaseOutlined className="text-3xl text-blue-600" />,
    title: "数据管理优化",
    description: "高效的数据存储和管理解决方案",
    highlights: ["数据备份恢复", "数据迁移", "存储优化", "数据治理"]
  },
  {
    icon: <SyncOutlined className="text-3xl text-blue-600" />,
    title: "系统集成能力",
    description: "与企业现有系统无缝集成",
    highlights: ["ERP系统集成", "CRM系统对接", "SSO单点登录", "数据同步"]
  },
  {
    icon: <BugOutlined className="text-3xl text-blue-600" />,
    title: "质量保证体系",
    description: "完善的测试和质量保证流程",
    highlights: ["自动化测试", "代码质量检查", "性能测试", "安全测试"]
  },
  {
    icon: <CustomerServiceOutlined className="text-3xl text-blue-600" />,
    title: "专业技术支持",
    description: "7x24小时专业技术支持服务",
    highlights: ["技术咨询", "故障排查", "系统优化", "培训服务"]
  }
];

const PrivateDeploymentFeatures: React.FC = () => {
  return (
    <div className="w-full max-w-6xl mx-auto py-20 px-4 lg:px-0">
      {/* 标题区域 */}
      <div className="text-center mb-16">
        <Title level={2} className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
          核心产品特性
        </Title>
        <Paragraph className="text-xl text-gray-600 max-w-3xl mx-auto">
          基于多年企业服务经验，为您提供全面、专业、可靠的私有化部署解决方案
        </Paragraph>
      </div>

      {/* 特性网格 */}
      <Row gutter={[24, 24]}>
        {features.map((feature, index) => (
          <Col xs={24} sm={12} lg={8} key={index}>
            <Card
              className="h-full hover:shadow-lg transition-shadow duration-300 border-gray-200"
              styles={{ body: { padding: '24px' } }}
            >
              <div className="text-center mb-4">
                {feature.icon}
              </div>
              
              <Title level={4} className="text-center text-gray-900 mb-3">
                {feature.title}
              </Title>
              
              <Paragraph className="text-gray-600 text-center mb-4">
                {feature.description}
              </Paragraph>
              
              <div className="space-y-2">
                {feature.highlights.map((highlight, idx) => (
                  <div key={idx} className="flex items-center text-sm text-gray-700">
                    <div className="w-1.5 h-1.5 bg-blue-600 rounded-full mr-2 flex-shrink-0"></div>
                    <span>{highlight}</span>
                  </div>
                ))}
              </div>
            </Card>
          </Col>
        ))}
      </Row>
    </div>
  );
};

export default PrivateDeploymentFeatures;
